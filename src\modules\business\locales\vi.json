{"business": {"title": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>h do<PERSON>h", "description": "<PERSON><PERSON><PERSON><PERSON> lý các ho<PERSON>t động kinh doanh của doanh nghi<PERSON>p", "common": {"status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "pending": "Chờ xử lý", "processing": "<PERSON><PERSON> lý", "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "cancelled": "<PERSON><PERSON> hủy", "blocked": "Đã chặn", "draft": "<PERSON><PERSON><PERSON>"}, "orderStatus": {"pending": "Chờ xử lý", "processing": "<PERSON><PERSON> lý", "confirmed": "<PERSON><PERSON> x<PERSON>c <PERSON>n", "shipped": "<PERSON><PERSON> gửi hàng", "delivered": "Đ<PERSON> giao hàng", "cancelled": "<PERSON><PERSON> hủy", "completed": "<PERSON><PERSON><PERSON> th<PERSON>"}, "paymentStatus": {"paid": "<PERSON><PERSON> thanh toán", "pending": "Chờ thanh toán", "unpaid": "<PERSON><PERSON><PERSON> to<PERSON>", "failed": "<PERSON><PERSON> to<PERSON> thất bại", "partiallyPaid": "<PERSON><PERSON> <PERSON><PERSON> một ph<PERSON>n", "refunded": "<PERSON><PERSON> hoàn tiền"}, "shippingStatus": {"pending": "<PERSON><PERSON> vận chuyển", "processing": "<PERSON><PERSON> bị", "shipped": "<PERSON><PERSON> gửi hàng", "delivered": "Đ<PERSON> giao hàng", "cancelled": "<PERSON><PERSON> hủy", "sorting": "Đang phân loại", "pickedUp": "<PERSON><PERSON> l<PERSON>y hàng", "inTransit": "<PERSON><PERSON> vận chuy<PERSON>n", "failed": "<PERSON><PERSON><PERSON> hàng thất b<PERSON>i", "returned": "<PERSON><PERSON> hoàn trả"}, "actions": {"save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "edit": "Chỉnh sửa", "create": "<PERSON><PERSON><PERSON> mới", "back": "Quay lại", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "submit": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "sort": "<PERSON><PERSON><PERSON>p", "add": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Xóa", "upload": "<PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "view": "Xem", "details": "<PERSON> ti<PERSON>", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "form": {"name": "Họ và tên", "email": "Email", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "address": "Địa chỉ", "description": "<PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "tags": "<PERSON><PERSON>ã<PERSON>", "notes": "<PERSON><PERSON><PERSON>"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "deleteError": "Có lỗi xảy ra khi xóa", "bulkDeleteSuccess": "<PERSON><PERSON><PERSON> thành công {{count}} mục", "bulkDeleteError": "Có lỗi xảy ra khi xóa", "loadError": "Lỗi khi tải dữ liệu"}}, "customer": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý thông tin khách hàng", "add": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> h<PERSON>ng", "edit": "Chỉnh sửa kh<PERSON>ch hàng", "view": "<PERSON>em chi tiết kh<PERSON>ch hàng", "addForm": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch hàng mới", "editForm": "Chỉnh sửa thông tin khách hàng", "detailForm": "<PERSON> tiết kh<PERSON>ch hàng", "totalCustomers": "Tổng số khách hàng", "manage": "<PERSON><PERSON><PERSON><PERSON> lý kh<PERSON>ch hàng", "platform": "<PERSON><PERSON><PERSON> t<PERSON>", "timezone": "<PERSON><PERSON><PERSON> giờ", "form": {"namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> họ và tên khách hàng", "emailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email", "phonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "tagsPlaceholder": "Nhập tag và nhấn Enter", "addressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "avatar": "Ảnh đại diện"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo khách hàng", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t khách hàng thành công", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật kh<PERSON>ch hàng", "deleteSuccess": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng thành công", "deleteError": "Có lỗi xảy ra khi xóa khách hàng", "bulkDeleteSuccess": "<PERSON><PERSON><PERSON> thành công {{count}} kh<PERSON>ch hàng", "bulkDeleteError": "Có lỗi xảy ra khi xóa khách hàng"}, "bulkDeleteConfirmation": "Bạn có chắc chắn muốn xóa {{count}} khách hàng đã chọn?", "detail": {"generalInfo": "Th<PERSON>ng tin chung", "social": "Mạng xã hội", "customFields": "Trường tùy chỉnh", "orders": "<PERSON><PERSON><PERSON> hàng", "activities": "<PERSON><PERSON><PERSON> đ<PERSON>", "overview": "<PERSON><PERSON><PERSON> quan", "interactions": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> tác", "totalOrders": "<PERSON><PERSON><PERSON> đơn hàng", "totalSpent": "T<PERSON>ng chi tiêu", "averageOrderValue": "<PERSON><PERSON><PERSON> trị đơn hàng trung bình", "lastOrderDate": "<PERSON><PERSON><PERSON> hàng <PERSON>", "customerSince": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng từ", "interactionChannels": "<PERSON><PERSON><PERSON> t<PERSON> tác", "orderHistory": "<PERSON><PERSON><PERSON> sử mua hàng", "activityLog": "<PERSON><PERSON><PERSON><PERSON> ký hoạt động", "socialProfiles": "<PERSON><PERSON> sơ mạng xã hội", "customFieldValues": "<PERSON><PERSON><PERSON> trị trường tùy chỉnh", "noData": "Chưa có dữ liệu", "noOrders": "<PERSON><PERSON><PERSON> có đơn hàng nào", "noOrdersDesc": "<PERSON><PERSON><PERSON><PERSON> hàng này chưa thực hiện đơn hàng nào", "noActivities": "Chưa có hoạt động nào", "noActivitiesDesc": "<PERSON><PERSON><PERSON><PERSON> hàng này chưa có hoạt động nào đư<PERSON><PERSON> ghi nhận", "noInteractions": "<PERSON><PERSON><PERSON> có tương tác nào", "noInteractionsDesc": "<PERSON><PERSON><PERSON><PERSON> hàng này chưa có tương tác nào đư<PERSON><PERSON> ghi nhận", "allActivities": "<PERSON><PERSON><PERSON> cả hoạt động", "revenue": "<PERSON><PERSON>h thu", "topChannels": "Top kênh nhận tin nhắn", "topDevices": "Top thiết bị khách hàng sử dụng", "interactedFlows": "Flow đã tương tác", "interactedCampaigns": "Campaign đã tương tác", "orderList": "<PERSON><PERSON><PERSON> sử mua hàng", "orderCode": "<PERSON><PERSON> đơn hàng", "orderDate": "Ngày đặt", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "deliveryStatus": "<PERSON><PERSON><PERSON><PERSON> thái vận chuyển", "source": "<PERSON><PERSON><PERSON><PERSON>", "totalAmount": "<PERSON><PERSON><PERSON> tiền", "orderStatus": "<PERSON><PERSON><PERSON><PERSON> thái đơn hàng", "paymentStatus": "<PERSON>r<PERSON><PERSON> thái thanh toán", "shippingStatus": "<PERSON><PERSON><PERSON><PERSON> thái vận chuyển", "flowName": "Tên flow", "lastInteraction": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c cu<PERSON>i", "campaignName": "Tên campaign", "interactionType": "<PERSON><PERSON><PERSON> tư<PERSON> tác", "sent": "Đ<PERSON> gửi", "opened": "Đã nhận", "clicked": "Đã click", "platforms": "<PERSON><PERSON><PERSON> tảng", "channels": "k<PERSON><PERSON>", "channelStats": "<PERSON><PERSON><PERSON><PERSON> kê kênh", "allInteractions": "<PERSON><PERSON><PERSON> cả tương tác", "interactionSummary": "Tổng cộng {{total}} tương tác qua {{channels}} kênh, {{completed}} ho<PERSON>n thành", "noSocialProfiles": "Chưa có mạng xã hội", "noSocialProfilesDesc": "<PERSON><PERSON><PERSON><PERSON> hàng này chưa có thông tin mạng xã hội nào", "items": "<PERSON><PERSON><PERSON> p<PERSON>m", "itemsUnit": "s<PERSON><PERSON> p<PERSON>m"}, "activity": {"type": "<PERSON><PERSON><PERSON> ho<PERSON>t động", "date": "<PERSON><PERSON><PERSON>", "details": "<PERSON> ti<PERSON>", "moreDetails": "chi ti<PERSON><PERSON>c", "types": {"order": "<PERSON><PERSON><PERSON> hàng", "login": "<PERSON><PERSON><PERSON>", "support": "Hỗ trợ", "review": "Đánh giá"}}, "order": {"cancel": "<PERSON><PERSON><PERSON> đơn hàng"}, "overview": {"flowCount": "Số flow", "campaignCount": "Số campaign", "sequenceCount": "Số sequence", "interactions": "Số tương tác", "revenue": "<PERSON><PERSON>h thu", "flows": "Flow", "campaigns": "Campaign", "sequences": "Sequence", "noChannelData": "<PERSON><PERSON>a có dữ liệu kênh", "noDeviceData": "<PERSON><PERSON><PERSON> có dữ liệu thiết bị", "topChannels": "Top kênh nhận tin nhắn", "topDevices": "Top thiết bị khách hàng sử dụng"}, "social": {"platform": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "link": "Link", "editDescription": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin mạng xã hội của khách hàng", "save": "<PERSON><PERSON><PERSON>", "addProfiles": "<PERSON><PERSON><PERSON><PERSON> mạng xã hội"}, "interaction": {"type": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "channel": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "date": "<PERSON><PERSON><PERSON>", "types": {"email": "Email", "phone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "chat": "Cha<PERSON>", "social": "Mạng xã hội", "meeting": "<PERSON><PERSON><PERSON><PERSON>"}, "statuses": {"completed": "<PERSON><PERSON><PERSON> th<PERSON>", "pending": "<PERSON><PERSON> lý", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}}, "import": {"title": "Import kh<PERSON>ch hàng", "steps": {"upload": "<PERSON><PERSON><PERSON>", "mapping": "<PERSON><PERSON> cột", "preview": "<PERSON><PERSON>", "importing": "Đang import"}, "upload": {"title": "<PERSON><PERSON><PERSON> lên file Excel", "description": "Chọn file Excel hoặc nhập URL để import danh sách khách hàng", "fromFile": "Từ file", "fromUrl": "Từ URL", "dragDrop": "Kéo thả file vào đây hoặc click để chọn", "supportedFormats": "Hỗ trợ: .xlsx, .xls, .csv (tối đa 10MB)", "selectFile": "<PERSON><PERSON><PERSON> file", "urlTitle": "Import từ URL", "urlPlaceholder": "Nhập URL file Excel...", "excelUrl": "URL file Excel", "sheetName": "Tên sheet", "sheetNamePlaceholder": "Nhập tên sheet (để trống sẽ lấy sheet đầu tiên)", "sheetNameHelp": "<PERSON><PERSON> trống sẽ sử dụng sheet đầu tiên trong file", "hasHeader": "File có dòng tiêu đề", "loadFromUrl": "<PERSON><PERSON><PERSON> từ URL", "dragDropTitle": "Kéo thả file vào đây", "dragDropDescription": "hoặc click để chọn file từ máy tính", "loading": "<PERSON><PERSON> tả<PERSON>..."}, "mapping": {"title": "<PERSON><PERSON> xạ cột dữ liệu", "description": "<PERSON><PERSON><PERSON> cột Excel tương ứng với từng trườ<PERSON> kh<PERSON>ch hàng", "columnMapping": "<PERSON><PERSON> cột", "selectField": "<PERSON><PERSON><PERSON> tr<PERSON>", "skipColumn": "Bỏ qua cột này", "requiredField": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "dataPreview": "<PERSON><PERSON> tr<PERSON><PERSON> dữ liệu", "previewNote": "<PERSON><PERSON><PERSON> thị 5 dòng đầu tiên", "validationErrors": "Lỗi validation", "errors": {"requiredFieldMissing": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> b<PERSON><PERSON> buộ<PERSON>: {{field}}", "duplicateMapping": "<PERSON><PERSON> trường được ánh xạ trùng lặp"}}, "preview": {"title": "Xem trước dữ liệu import", "description": "<PERSON><PERSON>m tra dữ liệu trước khi import vào hệ thống", "totalRows": "Tổng số dòng", "validRows": "<PERSON><PERSON><PERSON> h<PERSON>p l<PERSON>", "invalidRows": "Dòng lỗi", "validationWarnings": "<PERSON><PERSON><PERSON> báo validation", "dataPreview": "<PERSON><PERSON> tr<PERSON><PERSON> dữ liệu", "showingFirst10": "<PERSON><PERSON><PERSON> thị 10 dòng đầu tiên", "importOptions": "T<PERSON>y <PERSON> import", "skipInvalidRows": "Bỏ qua các dòng lỗi", "sendWelcomeEmail": "Gửi email chào mừng", "startImport": "Bắt đầu import", "row": "Dòng"}, "progress": {"importing": "Đang import d<PERSON> liệu", "pleaseWait": "<PERSON><PERSON> lòng đợi trong khi hệ thống xử lý dữ liệu", "processing": "<PERSON><PERSON> lý", "imported": "Đã import", "errors": "Lỗi", "recentErrors": "Lỗi gần đây"}, "complete": {"title": "Import hoàn thành", "description": "Quá trình import đã hoàn thành thành công", "totalProcessed": "Tổng đã xử lý", "successfullyImported": "Import thành công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "errorDetails": "<PERSON> tiết lỗi", "nextSteps": "<PERSON><PERSON><PERSON><PERSON> tiế<PERSON> theo", "reviewCustomers": "<PERSON><PERSON> lại danh s<PERSON>ch kh<PERSON>ch hàng", "setupSegments": "<PERSON><PERSON><PERSON><PERSON> lập phân khúc khách hàng", "createCampaigns": "Tạo chiến dịch marketing", "viewCustomers": "<PERSON><PERSON> h<PERSON>ng"}, "validation": {"nameRequired": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng là bắt buộc", "invalidEmail": "<PERSON><PERSON> h<PERSON> l<PERSON>", "invalidPhone": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ"}, "errors": {"emptyFile": "File rỗng hoặc không có dữ liệu", "invalidFile": "File không hợp lệ hoặc bị lỗi", "readError": "Lỗi khi đọc file", "invalidFileType": "Định dạng file không được hỗ trợ", "fileTooLarge": "File quá lớn (tối đa 10MB)", "parseError": "Lỗi khi phân tích dữ liệu", "urlRequired": "URL là bắt buộc", "urlFetchError": "<PERSON><PERSON><PERSON><PERSON> thể tải file từ URL", "urlLoadError": "Lỗi khi tải file từ URL"}}}, "customField": {"configId": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> đ<PERSON>nh danh", "title": "Trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON><PERSON> lý các trường tùy chỉnh", "adminDescription": "<PERSON><PERSON><PERSON><PERSON> lý các trường tùy chỉnh của hệ thống", "add": "Thêm trường tùy chỉnh", "edit": "Chỉnh sửa trường tùy chỉnh", "addForm": "Thêm trường tùy chỉnh mới", "editForm": "Chỉnh sửa trường tùy chỉnh", "component": "<PERSON><PERSON><PERSON> thành phần", "components": {"input": "Ô nhập liệu", "textarea": "Ô văn bản", "select": "<PERSON><PERSON>n", "checkbox": "<PERSON><PERSON><PERSON>", "radio": "Nút radio", "date": "<PERSON><PERSON><PERSON>", "number": "Số", "file": "<PERSON><PERSON>p tin", "multiSelect": "<PERSON><PERSON><PERSON>"}, "type": "<PERSON><PERSON><PERSON> dữ liệu", "type.string": "<PERSON><PERSON><PERSON>", "type.number": "Số", "type.boolean": "Có/<PERSON>hông", "type.date": "<PERSON><PERSON><PERSON>", "type.object": "<PERSON><PERSON><PERSON>", "type.array": "<PERSON><PERSON><PERSON>", "types": {"text": "<PERSON><PERSON><PERSON>", "number": "Số", "boolean": "Có/<PERSON>hông", "date": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "object": "<PERSON><PERSON><PERSON>", "array": "<PERSON><PERSON><PERSON>", "string": "<PERSON><PERSON><PERSON>"}, "name": "<PERSON><PERSON><PERSON> tr<PERSON>", "label": "<PERSON><PERSON>ã<PERSON>", "placeholder": "Placeholder", "defaultValue": "<PERSON><PERSON><PERSON> trị mặc định", "options": "<PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "validation": {"minLength": "<PERSON><PERSON> dài tối thiểu", "maxLength": "<PERSON><PERSON> dài tối đa", "pattern": "Mẫu kiểm tra", "min": "<PERSON><PERSON><PERSON> trị tối thiểu", "max": "<PERSON><PERSON><PERSON> trị tối đa"}, "form": {"componentRequired": "<PERSON><PERSON> lòng chọn loại thành phần", "labelRequired": "<PERSON><PERSON> lòng nh<PERSON><PERSON> nh<PERSON>n", "typeRequired": "<PERSON><PERSON> lòng chọn kiểu dữ liệu", "idRequired": "<PERSON><PERSON> lòng nhập tên trường định danh", "labelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>n hiển thị", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho trường này", "placeholderPlaceholder": "<PERSON>h<PERSON>p placeholder", "defaultValuePlaceholder": "<PERSON><PERSON><PERSON><PERSON> giá trị mặc định", "optionsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> các tù<PERSON>, ph<PERSON> cách bằng dấu phẩy hoặc định dạng JSON", "selectOptionsPlaceholder": "<PERSON>h<PERSON>p giá trị theo cấu trúc: Name|Value, Mỗi cặp giá trị trên 1 dòng. VD:\na|1\nb|2", "booleanDefaultPlaceholder": "<PERSON><PERSON><PERSON> giá trị mặc định", "dateDefaultPlaceholder": "<PERSON><PERSON><PERSON> ngày mặc định", "description": "<PERSON><PERSON>", "labelTagRequired": "<PERSON><PERSON> lòng thêm ít nhất một nhãn", "fieldIdLabel": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> đ<PERSON>nh danh", "fieldIdPlaceholder": "text-input-001", "displayNameLabel": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> hiển thị", "displayNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên hiển thị cho trường này", "displayNameRequired": "<PERSON><PERSON> lòng nhập tên trường hiển thị", "labelInputPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>n và nhấn Enter", "tagsCount": "nh<PERSON>n đã thêm", "patternSuggestions": "Gợi ý pattern phổ biến:", "defaultValue": "<PERSON><PERSON><PERSON> trị mặc định", "minLength": "<PERSON><PERSON> dài tối thiểu", "maxLength": "<PERSON><PERSON> dài tối đa", "pattern": "Mẫu kiểm tra", "options": "<PERSON><PERSON><PERSON>", "min": "<PERSON><PERSON><PERSON> trị tối thiểu", "max": "<PERSON><PERSON><PERSON> trị tối đa", "placeholder": "Placeholder", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "labels": "<PERSON><PERSON>ã<PERSON>"}, "createSuccess": "Tạo trường tùy chỉnh thành công", "createError": "Lỗi khi tạo trường tùy chỉnh", "updateSuccess": "<PERSON><PERSON><PERSON> nhật trường tùy chỉnh thành công", "updateError": "Lỗi khi cập nhật trường tùy chỉnh", "deleteSuccess": "<PERSON>óa trường tùy chỉnh thành công", "deleteError": "Lỗi khi xóa trường tùy chỉnh", "loadError": "Lỗi khi tải trường tùy chỉnh", "booleanValues": {"true": "<PERSON><PERSON>", "false": "K<PERSON>ô<PERSON>"}, "patterns": {"email": "Email", "phoneVN": "Số điện thoại VN", "phoneIntl": "<PERSON><PERSON> điện thoại quốc tế", "postalCodeVN": "<PERSON><PERSON> b<PERSON><PERSON> ch<PERSON> VN", "lettersOnly": "Chỉ chữ cái", "numbersOnly": "Chỉ số", "alphanumeric": "Chữ và số", "noSpecialChars": "<PERSON><PERSON><PERSON><PERSON> có ký tự đặc biệt", "url": "URL", "ipv4": "IPv4", "strongPassword": "<PERSON><PERSON><PERSON><PERSON> mạnh", "vietnameseName": "<PERSON><PERSON><PERSON> (c<PERSON>)", "studentId": "<PERSON><PERSON> sinh viên", "nationalId": "CMND/CCCD", "taxCode": "<PERSON><PERSON> số thuế", "dateFormat": "<PERSON>ày (dd/mm/yyyy)", "timeFormat": "Giờ (hh:mm)", "hexColor": "Hex color", "base64": "Base64", "uuid": "UUID", "filename": "Tên file", "urlSlug": "Slug URL", "variableName": "<PERSON><PERSON><PERSON>", "creditCard": "Số thẻ tín dụng", "qrCode": "Mã QR", "gpsCoordinate": "Tọa độ GPS", "rgbColor": "Mã màu RGB", "domain": "<PERSON><PERSON><PERSON>", "decimal": "<PERSON><PERSON> thập phân", "barcode": "Mã vạch"}, "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa trường tùy chỉnh này?", "bulkDeleteSuccess": "<PERSON><PERSON> xóa thành công {{count}} trường tùy chỉnh", "bulkDeleteError": "C<PERSON> lỗi xảy ra khi xóa trường tùy chỉnh", "bulkDeleteConfirmMessage": "Bạn có chắc chắn muốn xóa {{count}} trường tùy chỉnh đã chọn?", "selectedItems": "<PERSON><PERSON> chọn {{count}} mục", "totalFields": "Tổng số trường tùy chỉnh", "manage": "<PERSON><PERSON><PERSON><PERSON> lý trường tùy chỉnh"}, "customGroupForm": {"title": "Nhóm trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON><PERSON> lý các nhóm trường tùy chỉnh", "addForm": "Thêm nhóm trường tùy chỉnh", "editForm": "Chỉnh sửa nhóm trường tùy chỉnh", "createSuccess": "<PERSON><PERSON><PERSON> nhóm trường tùy chỉnh thành công", "createError": "Lỗi khi tạo nhóm trường tùy chỉnh", "updateSuccess": "<PERSON><PERSON><PERSON> nhật nhóm trường tùy chỉnh thành công", "updateError": "Lỗi khi cập nhật nhóm trường tùy chỉnh", "deleteSuccess": "<PERSON><PERSON><PERSON> nhóm trường tùy chỉnh thành công", "deleteError": "Lỗi khi xóa nhóm trường tùy chỉnh", "loadError": "Lỗi khi tải nhóm trường tùy chỉnh", "form": {"label": "<PERSON><PERSON><PERSON>", "labelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên nhóm trường tùy chỉnh", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho nhóm trường tùy chỉnh", "fieldCount": "Số trường", "fields": "tr<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "subtitle": "Tạo và quản lý nhóm các trường tùy chỉnh để sử dụng trong sản phẩm", "validation": {"labelRequired": "<PERSON><PERSON><PERSON> n<PERSON> là bắ<PERSON> bu<PERSON>c", "labelMinLength": "Tên nhóm ph<PERSON>i có ít nhất 2 ký tự", "labelMaxLength": "<PERSON>ên nhóm không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 255 ký tự", "descriptionMaxLength": "<PERSON><PERSON> tả không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 500 ký tự"}}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "draft": "<PERSON><PERSON><PERSON>"}}, "order": {"title": "<PERSON><PERSON><PERSON> hàng", "description": "<PERSON><PERSON><PERSON><PERSON> lý đơn hàng", "adminDescription": "<PERSON><PERSON><PERSON><PERSON> lý và theo dõi đơn hàng của người dùng", "createOrder": "<PERSON><PERSON><PERSON> đơn hàng mới", "editOrder": "Chỉnh sửa đơn hàng", "viewOrder": "<PERSON>em chi tiết đơn hàng", "orderNumber": "<PERSON><PERSON> đơn hàng", "customerInfo": "Thông tin khách hàng", "customerName": "<PERSON><PERSON><PERSON> h<PERSON>ng", "customerEmail": "Email", "customerPhone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "customerAddress": "Địa chỉ", "items": "<PERSON><PERSON><PERSON> phẩm trong đơn hàng", "noItems": "<PERSON><PERSON><PERSON><PERSON> có sản phẩm nào trong đơn hàng", "quantity": "Số lượng", "totalAmount": "<PERSON><PERSON><PERSON> tiền", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "paymentMethods": {"cash": "Tiền mặt", "creditCard": "Thẻ tín dụng", "bankTransfer": "<PERSON><PERSON><PERSON><PERSON>", "digitalWallet": "<PERSON><PERSON> điện tử"}, "status": {"pending": "Chờ xử lý", "processing": "<PERSON><PERSON> lý", "confirmed": "<PERSON><PERSON> x<PERSON>c <PERSON>n", "shipped": "<PERSON><PERSON> gửi hàng", "delivered": "Đ<PERSON> giao hàng", "cancelled": "<PERSON><PERSON> hủy", "completed": "<PERSON><PERSON><PERSON> th<PERSON>"}, "paymentStatus": {"paid": "<PERSON><PERSON> thanh toán", "pending": "Chờ thanh toán", "unpaid": "<PERSON><PERSON><PERSON> to<PERSON>", "failed": "<PERSON><PERSON> to<PERSON> thất bại", "partiallyPaid": "<PERSON><PERSON> <PERSON><PERSON> một ph<PERSON>n"}, "shippingStatus": {"pending": "<PERSON><PERSON> vận chuyển", "processing": "<PERSON><PERSON> bị", "shipped": "<PERSON><PERSON> gửi hàng", "delivered": "Đ<PERSON> giao hàng", "cancelled": "<PERSON><PERSON> hủy", "sorting": "Đang phân loại"}, "totalPrice": "<PERSON><PERSON><PERSON><PERSON> tiền", "price": "Đơn giá", "orderSummary": "<PERSON><PERSON><PERSON> tắt đơn hàng", "additionalInfo": "<PERSON><PERSON><PERSON><PERSON> tin bổ sung", "shippingInfo": "Thông tin vận chuyển", "paymentInfo": "Thông tin thanh toán", "selectCustomer": "<PERSON><PERSON><PERSON> kh<PERSON> h<PERSON>ng", "createNewCustomer": "<PERSON><PERSON><PERSON> k<PERSON>ch hàng mới", "searchCustomerPlaceholder": "<PERSON><PERSON><PERSON> kiếm kh<PERSON>ch hàng...", "searchResults": "<PERSON><PERSON><PERSON> qu<PERSON> tìm kiếm", "noCustomersFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy khách hàng", "selectProducts": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "searchProductPlaceholder": "<PERSON><PERSON><PERSON> kiếm sản phẩm...", "selectedProducts": "<PERSON><PERSON><PERSON> phẩm đã chọn", "noProductsSelected": "<PERSON><PERSON><PERSON> chọn sản phẩm nào", "noProductsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm", "searchToAddProducts": "<PERSON><PERSON><PERSON> kiếm để thêm sản phẩm vào đơn hàng", "print": "In đơn hàng", "uploading": "<PERSON><PERSON> tải lên...", "summaryNote": "<PERSON><PERSON> lòng kiểm tra kỹ thông tin trước khi tạo đơn hàng", "createSuccessDescription": "Đơn hàng đã được tạo thành công và sẽ được xử lý sớm nhất", "createErrorDescription": "<PERSON><PERSON> lỗi xảy ra trong quá trình tạo đơn hàng, vui lòng thử lại", "steps": {"customer": "<PERSON><PERSON><PERSON><PERSON>", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "delivery": "<PERSON><PERSON><PERSON>", "shipping": "<PERSON><PERSON><PERSON> ch<PERSON>", "payment": "<PERSON><PERSON> toán", "review": "<PERSON><PERSON>"}, "shipping": {"self": "<PERSON>ự vận chuyển", "ghn": "<PERSON><PERSON><PERSON>", "ghtk": "<PERSON><PERSON><PERSON>"}, "shippingService": "<PERSON><PERSON><PERSON> v<PERSON> vận chuy<PERSON>n", "shippingServicePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên dịch vụ vận chuyển", "selectProductsFirst": "<PERSON><PERSON> lòng chọn sản phẩm trước", "multipleProductTypes": "<PERSON><PERSON><PERSON> hàng có nhiều loại sản phẩm", "multipleProductTypesDescription": "Đơn hàng này chứa nhiều loại sản phẩm khác <PERSON>hau, vui lòng điền thông tin giao hàng cho từng loại.", "deliverySummary": "<PERSON><PERSON><PERSON> tắt giao hàng", "physicalShippingRequired": "<PERSON>ần vận chuyển sản phẩm vật lý", "digitalDeliveryRequired": "<PERSON><PERSON><PERSON> giao hàng sản phẩm số", "serviceDeliveryRequired": "<PERSON><PERSON><PERSON> thông tin dịch vụ", "eventDeliveryRequired": "<PERSON><PERSON><PERSON> thông tin sự kiện", "addTag": "<PERSON><PERSON><PERSON><PERSON>", "removeTag": "<PERSON><PERSON><PERSON>", "form": {"customerNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên kh<PERSON>ch hàng", "customerEmailPlaceholder": "Nhập email kh<PERSON>ch hàng", "customerPhonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện tho<PERSON><PERSON> kh<PERSON>ch hàng", "customerAddressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ khách hàng", "notesPlaceholder": "<PERSON><PERSON><PERSON><PERSON> ghi chú cho đơn hàng", "tagsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>n và nhấn Enter"}, "createSuccess": "<PERSON><PERSON><PERSON> đơn hàng thành công", "createError": "Lỗi khi tạo đơn hàng", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t đơn hàng thành công", "updateError": "Lỗi khi cập nhật đơn hàng", "deleteSuccess": "<PERSON><PERSON><PERSON> đơn hàng thành công", "deleteError": "Lỗi khi xóa đơn hàng", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa đơn hàng này?", "digitalDelivery": {"title": "<PERSON><PERSON><PERSON> h<PERSON> sản phẩm số", "method": "<PERSON><PERSON><PERSON><PERSON> thức giao hàng", "recipient": "<PERSON><PERSON><PERSON><PERSON>n", "message": "<PERSON>", "scheduledDelivery": "<PERSON><PERSON><PERSON><PERSON> gian giao hàng", "emailAddress": "Đ<PERSON>a chỉ email", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "telegramUsername": "<PERSON><PERSON><PERSON> dùng Telegram", "downloadEmail": "Email nhận link tải", "emailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email", "phonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "telegramPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON>i dùng Telegram", "downloadLinkPlaceholder": "Nhập email để nhận link tải", "recipientPlaceholder": "<PERSON><PERSON><PERSON><PERSON> thông tin người nhận", "messagePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tin nhắn kèm theo (tù<PERSON> chọn)", "methods": {"email": "Email", "sms": "SMS", "zalo": "<PERSON><PERSON>", "telegram": "Telegram", "whatsapp": "WhatsApp", "downloadLink": "<PERSON> t<PERSON>"}}, "serviceDelivery": {"title": "Thông tin dịch vụ", "serviceDate": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> hi<PERSON>n d<PERSON>ch vụ", "serviceDatePlaceholder": "<PERSON><PERSON><PERSON> ngày thực hiện dịch vụ", "serviceLocation": "<PERSON><PERSON><PERSON> đi<PERSON><PERSON> thự<PERSON> hi<PERSON>n", "serviceLocationPlaceholder": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> điểm thực hiện dịch vụ", "contactPerson": "<PERSON><PERSON><PERSON><PERSON> li<PERSON>n hệ", "contactPersonPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON>i liên hệ", "contactPhone": "<PERSON><PERSON> điện thoại liên hệ", "contactPhonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại liên hệ", "serviceNotes": "<PERSON><PERSON> chú d<PERSON>ch vụ", "serviceNotesPlaceholder": "<PERSON><PERSON><PERSON><PERSON> ghi chú về dịch vụ", "instructions": "Hướng dẫn", "instructionsText": "<PERSON><PERSON> lòng cung cấp đầy đủ thông tin để chúng tôi có thể liên hệ và thực hiện dịch vụ tốt nhất."}, "eventDelivery": {"title": "Thông tin sự kiện", "eventInfo": "Thông tin sự kiện", "eventDate": "<PERSON><PERSON><PERSON> kiện", "eventDatePlaceholder": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> diễn ra sự kiện", "eventLocation": "<PERSON><PERSON><PERSON> điểm sự kiện", "eventLocationPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên địa điểm sự kiện", "eventAddress": "Địa chỉ sự kiện", "eventAddressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ chi tiết của sự kiện", "ticketDelivery": "Giao vé", "ticketDeliveryMethod": "<PERSON><PERSON><PERSON><PERSON> thức giao vé", "ticketRecipient": "Ngư<PERSON><PERSON> n<PERSON>n vé", "ticketMethods": {"email": "<PERSON><PERSON><PERSON> qua Email", "sms": "<PERSON><PERSON><PERSON> qua SMS", "pickup": "<PERSON><PERSON><PERSON><PERSON> tại chỗ", "mail": "<PERSON><PERSON><PERSON> qua bưu đi<PERSON>n"}, "attendeeInfo": "Th<PERSON>ng tin người tham dự", "attendeeName": "<PERSON><PERSON><PERSON> tham dự", "attendeeNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON> tham dự", "attendeePhone": "<PERSON><PERSON> điện thoại người tham dự", "attendeePhonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại người tham dự", "attendeeEmail": "<PERSON><PERSON> tham dự", "attendeeEmailPlaceholder": "<PERSON><PERSON>ập email ngư<PERSON>i tham dự", "eventNotes": "<PERSON><PERSON> chú sự kiện", "eventNotesPlaceholder": "<PERSON><PERSON><PERSON><PERSON> ghi chú về sự kiện", "emailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email", "phonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "recipientPlaceholder": "<PERSON><PERSON><PERSON><PERSON> thông tin người nhận", "instructions": "Hướng dẫn", "instructionsText": "<PERSON><PERSON> lòng cung cấp đầy đủ thông tin để chúng tôi có thể gửi vé và thông báo về sự kiện."}}, "conversion": {"title": "<PERSON><PERSON><PERSON><PERSON> đổi", "description": "<PERSON> và quản lý các chuyển đổi", "adminDescription": "<PERSON> và quản lý các bản ghi chuyển đổi", "totalConversions": "Tổng số chuyển đổi", "manage": "<PERSON><PERSON><PERSON><PERSON> lý chuyển đổi", "id": "ID", "customerId": "ID khách hàng", "userId": "ID người dùng", "type": "<PERSON><PERSON><PERSON> chuyển đổi", "name": "<PERSON><PERSON><PERSON>", "source": "<PERSON><PERSON><PERSON><PERSON>", "destination": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON> trị", "date": "<PERSON><PERSON><PERSON>", "status": {"completed": "<PERSON><PERSON><PERSON> th<PERSON>", "pending": "<PERSON><PERSON> lý", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}}, "warehouseCustomField": {"title": "Trường tùy chỉnh kho", "description": "<PERSON><PERSON><PERSON><PERSON> lý các trường tùy chỉnh của kho"}, "report": {"title": "Báo cáo", "description": "<PERSON><PERSON> c<PERSON>c báo cáo kinh doanh", "totalReports": "Tổng số báo cáo", "view": "<PERSON><PERSON> b<PERSON>o c<PERSON>o", "tabs": {"sales": "<PERSON><PERSON>h thu", "orders": "<PERSON><PERSON><PERSON> hàng", "customers": "<PERSON><PERSON><PERSON><PERSON>", "products": "<PERSON><PERSON><PERSON> p<PERSON>m"}, "charts": {"salesPlaceholder": "Biểu đồ doanh thu sẽ được hiển thị ở đây", "ordersPlaceholder": "<PERSON>i<PERSON>u đồ đơn hàng sẽ được hiển thị ở đây", "customersPlaceholder": "<PERSON><PERSON><PERSON><PERSON> đồ khách hàng sẽ được hiển thị ở đây", "productsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> đồ sản phẩm sẽ được hiển thị ở đây"}}, "inventory": {"title": "<PERSON><PERSON><PERSON><PERSON> lý kho", "description": "<PERSON><PERSON><PERSON><PERSON> lý hàng tồn kho và nhập xuất kho", "totalItems": "Tổng số mặt hàng", "totalProducts": "Tổng số sản phẩm", "manage": "<PERSON><PERSON><PERSON><PERSON> lý kho", "currentQuantity": "S<PERSON> l<PERSON> hiện tại", "availableQuantity": "<PERSON><PERSON> lượng có sẵn", "reservedQuantity": "Số lượng đã đặt trước", "defectiveQuantity": "S<PERSON> lượng hỏng", "totalQuantity": "Tổng số lượng", "updateQuantity": "<PERSON><PERSON><PERSON> nh<PERSON>t số lư<PERSON>", "addProduct": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "status": {"inStock": "<PERSON><PERSON><PERSON> hàng", "lowStock": "<PERSON><PERSON><PERSON>", "outOfStock": "<PERSON><PERSON><PERSON>"}}, "product": {"title": "<PERSON><PERSON><PERSON> p<PERSON>m", "description": "<PERSON><PERSON><PERSON><PERSON> lý danh sách sản phẩm", "adminDescription": "<PERSON><PERSON><PERSON><PERSON> lý và theo dõi sản phẩm của người dùng", "totalProducts": "Tổng số sản phẩm", "manage": "<PERSON><PERSON><PERSON><PERSON> lý sản phẩm", "name": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "image": "Ảnh", "tags": "Thẻ", "createProduct": "<PERSON><PERSON><PERSON> s<PERSON>n ph<PERSON>m", "editProduct": "Chỉnh s<PERSON>a sản phẩm", "productList": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m", "productDetails": "<PERSON> tiết sản phẩm", "productInfo": "Thông tin sản phẩm", "productAttributes": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> sản ph<PERSON>m", "productImages": "<PERSON><PERSON><PERSON> sản phẩm", "listPrice": "<PERSON><PERSON><PERSON>", "salePrice": "<PERSON><PERSON><PERSON> b<PERSON>", "currency": "Đơn vị tiền tệ", "priceDescription": "<PERSON>ô tả giá", "createSuccess": "<PERSON><PERSON><PERSON> sản phẩm thành công", "createError": "Lỗi khi tạo sản phẩm", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm thành công", "updateError": "Lỗi khi cập nh<PERSON>t sản phẩm", "deleteSuccess": "<PERSON><PERSON><PERSON> sản phẩm thành công", "deleteError": "Lỗi khi xóa sản phẩm", "bulkDeleteSuccess": "<PERSON><PERSON><PERSON> {{count}} sản phẩm thành công", "bulkDeleteError": "Lỗi khi xóa nhiều sản phẩm", "selectToDelete": "<PERSON><PERSON> lòng chọn ít nhất một sản phẩm để xóa", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa sản phẩm này?", "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa {{count}} sản phẩm đã chọn?", "priceType": {"title": "Loại giá", "hasPrice": "<PERSON><PERSON> giá cố định", "stringPrice": "<PERSON><PERSON><PERSON> dạng mô tả", "noPrice": "Không có giá"}, "productType": {"title": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "physical": "<PERSON><PERSON><PERSON>", "digital": "Số", "service": "<PERSON><PERSON><PERSON> v<PERSON>", "event": "<PERSON><PERSON> kiện", "combo": "Combo"}, "status": {"active": "<PERSON><PERSON> bán", "inactive": "<PERSON><PERSON><PERSON> b<PERSON>", "outOfStock": "<PERSON><PERSON><PERSON>", "draft": "<PERSON><PERSON><PERSON>"}, "fields": {"name": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "price": "Giá", "priceType": "Loại giá", "priceTypes": {"yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "regularPrice": "<PERSON><PERSON><PERSON>", "salePrice": "<PERSON><PERSON><PERSON> mãi", "priceNote": "<PERSON><PERSON> chú về giá", "brand": "<PERSON><PERSON><PERSON><PERSON>", "url": "URL", "description": "<PERSON><PERSON>", "attributes": "<PERSON><PERSON><PERSON><PERSON>", "attributeName": "<PERSON><PERSON><PERSON>", "attributeType": "<PERSON><PERSON><PERSON> dữ liệu", "attributeValue": "<PERSON><PERSON><PERSON> trị mặc định"}, "attributeTypes": {"text": "<PERSON><PERSON><PERSON>", "number": "Số", "date": "<PERSON><PERSON><PERSON>", "boolean": "Có/<PERSON>hông", "list": "<PERSON><PERSON>"}, "images": {"addImages": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>nh sản phẩm", "image": "Ảnh", "url": "URL", "video": "Video", "uploadImage": "<PERSON><PERSON><PERSON>", "enterImageUrl": "Nhập URL hình ảnh", "enterVideoUrl": "Nhập URL video", "recommendedSize": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> nghị: 800x600px, tối đa 2MB", "addToList": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>o danh s<PERSON>ch", "uploadedImages": "<PERSON><PERSON> t<PERSON> lên", "urlImages": "<PERSON><PERSON> s<PERSON> từ URL", "videoList": "<PERSON><PERSON> video", "setCover": "Đặt làm ảnh bìa", "coverImage": "Ảnh bìa", "uploadedFromComputer": "<PERSON><PERSON><PERSON> lên từ máy t<PERSON>h", "dragAndDrop": "<PERSON><PERSON>o thả hoặc click để tải lên ảnh sản phẩm"}, "actions": {"createProduct": "<PERSON><PERSON><PERSON> s<PERSON>n ph<PERSON>m", "saveProduct": "<PERSON><PERSON><PERSON> p<PERSON>m", "deleteProduct": "<PERSON><PERSON><PERSON> p<PERSON>m", "cancelCreation": "<PERSON><PERSON><PERSON>"}, "messages": {"productCreated": "<PERSON><PERSON><PERSON> sản phẩm thành công", "productUpdated": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm thành công", "productDeleted": "<PERSON><PERSON><PERSON> sản phẩm thành công", "confirmDelete": "Bạn có chắc chắn muốn xóa sản phẩm này không?"}, "import": {"title": "Import sản ph<PERSON>m", "steps": {"upload": "<PERSON><PERSON><PERSON> l<PERSON> file", "mapping": "<PERSON><PERSON> cột", "preview": "<PERSON><PERSON> tr<PERSON><PERSON> dữ liệu", "importing": "Đang import"}, "mapping": {"title": "<PERSON><PERSON> cột", "description": "<PERSON><PERSON> x<PERSON> các cột Excel với trường sản phẩm", "columnMapping": "<PERSON><PERSON> cột", "skipColumn": "Bỏ qua cột này", "requiredField": "Trư<PERSON>ng này là bắ<PERSON> buộc", "dataPreview": "<PERSON><PERSON> tr<PERSON><PERSON> dữ liệu", "validationErrors": "Lỗi xác thực", "errors": {"duplicateMapping": "Trường này đã được ánh xạ với cột khác", "requiredFieldMissing": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> buộ<PERSON> {{field}} ch<PERSON><PERSON> đ<PERSON><PERSON><PERSON> ánh xạ"}}, "upload": {"title": "<PERSON><PERSON><PERSON> lên file sản phẩm", "description": "Chọn file Excel hoặc nhập URL để import danh sách sản phẩm", "fromFile": "Từ file", "fromUrl": "Từ URL", "supportedFormats": "Hỗ trợ: .xlsx, .xls, .csv (tối đa 10MB)", "hasHeader": "File có dòng tiêu đề", "excelUrl": "URL file Excel", "urlPlaceholder": "Nhập URL file Excel...", "loading": "<PERSON><PERSON> tả<PERSON>...", "loadFromUrl": "<PERSON><PERSON><PERSON> từ URL"}, "errors": {"parseError": "Lỗi phân tích file", "urlRequired": "URL là bắt buộc", "urlFetchError": "Lỗi tải file từ URL", "urlLoadError": "Lỗi tải file từ URL"}, "progress": {"importing": "Đang import sản phẩm", "pleaseWait": "<PERSON><PERSON> lòng đợi trong khi hệ thống xử lý dữ liệu", "processing": "<PERSON><PERSON> lý", "imported": "Đã import", "errors": "Lỗi"}, "complete": {"title": "Import hoàn thành", "description": "Quá trình import sản phẩm đã hoàn thành thành công", "totalProcessed": "Tổng đã xử lý", "successfullyImported": "Import thành công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "errorDetails": "<PERSON> tiết lỗi", "nextSteps": "<PERSON><PERSON><PERSON><PERSON> tiế<PERSON> theo", "reviewProducts": "<PERSON><PERSON> lại danh s<PERSON>ch sản phẩm", "updateInventory": "<PERSON><PERSON><PERSON> nh<PERSON>t mức tồn kho", "setupCategories": "<PERSON><PERSON><PERSON><PERSON> lập danh mục sản phẩm", "viewProducts": "<PERSON><PERSON> p<PERSON>m"}, "preview": {"title": "Xem trước dữ liệu import", "description": "Xem lại và xác thực dữ liệu trư<PERSON><PERSON> khi import", "totalRows": "Tổng số dòng", "validRows": "<PERSON><PERSON><PERSON> h<PERSON>p l<PERSON>", "invalidRows": "Dòng lỗi", "validationWarnings": "<PERSON><PERSON><PERSON> b<PERSON>o x<PERSON>c thực", "dataPreview": "<PERSON><PERSON> tr<PERSON><PERSON> dữ liệu", "showingFirst10": "<PERSON><PERSON><PERSON> thị 10 dòng đầu tiên", "importOptions": "T<PERSON>y <PERSON> import", "skipInvalidRows": "Bỏ qua các dòng lỗi", "updateExisting": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm hiện có", "sendNotification": "<PERSON><PERSON><PERSON> thông báo", "startImport": "Bắt đầu import", "row": "Dòng"}, "validation": {"nameRequired": "<PERSON><PERSON><PERSON> sản phẩm là bắt buộc", "skuRequired": "SKU là bắt buộc", "priceRequired": "<PERSON><PERSON><PERSON> là b<PERSON> bu<PERSON>c", "invalidPrice": "<PERSON><PERSON><PERSON> ph<PERSON>i là số dương", "invalidStock": "<PERSON><PERSON><PERSON> kho phải là số không âm"}}, "form": {"title": "<PERSON><PERSON><PERSON><PERSON> sản phẩm mới", "name": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "description": "<PERSON><PERSON>", "price": "Giá", "category": "<PERSON><PERSON>", "sku": "Mã SKU", "statusField": "<PERSON><PERSON><PERSON><PERSON> thái", "inventory": "Số lư<PERSON>ng tồn kho", "submit": "<PERSON><PERSON><PERSON> p<PERSON>m", "cancel": "<PERSON><PERSON><PERSON>", "createTitle": "<PERSON><PERSON><PERSON><PERSON> sản phẩm vật lý", "editTitle": "Chỉnh s<PERSON>a sản phẩm", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên sản phẩm", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả sản phẩm", "pricePlaceholder": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> sản phẩm", "categoryPlaceholder": "<PERSON><PERSON><PERSON> danh mục sản phẩm", "skuPlaceholder": "<PERSON><PERSON>ậ<PERSON> mã SKU sản phẩm", "statusFieldPlaceholder": "<PERSON><PERSON><PERSON> trạng thái sản phẩm", "inventoryPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số lư<PERSON> tồn kho", "tagsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> thẻ sản phẩm", "mediaPlaceholder": "<PERSON><PERSON>o thả hoặc click để tải lên ảnh sản phẩm", "media": "Ảnh sản phẩm", "shipmentConfig": {"title": "<PERSON><PERSON><PERSON> hình vận chuyển", "widthCm": "<PERSON><PERSON>u rộng (cm)", "heightCm": "<PERSON><PERSON><PERSON> cao (cm)", "lengthCm": "<PERSON><PERSON><PERSON> dài (cm)"}, "customFields": {"title": "Trường tùy chỉnh", "selectField": "<PERSON><PERSON>n trường tùy chỉnh", "selectGroupForm": "<PERSON><PERSON><PERSON> nhóm trường tùy chỉnh", "searchPlaceholder": "T<PERSON>m kiếm trường tùy chỉnh...", "searchGroupPlaceholder": "Tìm kiếm nhóm trường tùy chỉnh...", "selectedFields": "Trường tùy chỉnh đã chọn", "selectedGroupForm": "Nhóm trường tùy chỉnh đã chọn", "addField": "Thêm trường tùy chỉnh", "addGroupForm": "Thêm nhóm trường tùy chỉnh", "valuePlaceholder": "<PERSON><PERSON><PERSON><PERSON> giá trị", "validation": {"required": "Trư<PERSON>ng này là bắ<PERSON> buộc", "minLength": "<PERSON><PERSON><PERSON> thiểu {{min}} ký tự", "maxLength": "T<PERSON>i đa {{max}} ký tự", "pattern": "<PERSON><PERSON><PERSON> dạng không hợp lệ", "invalidNumber": "<PERSON><PERSON><PERSON> là số hợp lệ", "min": "<PERSON><PERSON><PERSON> thiểu {{min}}", "max": "<PERSON><PERSON><PERSON> đa {{max}}", "invalidEmail": "<PERSON><PERSON> h<PERSON> l<PERSON>", "invalidUrl": "URL không hợp lệ", "invalidDate": "<PERSON><PERSON><PERSON> h<PERSON> l<PERSON>"}}, "variants": {"title": "Phân loại mẫu mã", "addVariant": "<PERSON>hê<PERSON> phân lo<PERSON>i", "variant": "<PERSON><PERSON> lo<PERSON>", "noVariants": "<PERSON><PERSON><PERSON> có phân loại nào. Nhấn \"Thêm phân loại\" để bắt đầu.", "customFields": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h phân lo<PERSON>i", "searchCustomField": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> tả biến thể", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho biến thể này", "priceDescription": "<PERSON>ô tả giá", "priceDescriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả giá cho biến thể này"}, "versions": {"title": "<PERSON><PERSON><PERSON>", "addVersion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bản", "version": "<PERSON><PERSON><PERSON>", "noVersions": "<PERSON><PERSON><PERSON> có phiên bản nào. <PERSON><PERSON><PERSON><PERSON> \"Thêm phiên bản\" để bắt đầu.", "name": "<PERSON><PERSON><PERSON> p<PERSON>ê<PERSON> bản", "namePlaceholder": "Basic, Pro, Premium...", "price": "Giá", "currency": "Đơn vị tiền tệ", "description": "<PERSON><PERSON> t<PERSON> phiên bản", "descriptionPlaceholder": "<PERSON><PERSON> tả chi tiết về phiên bản nà<PERSON>...", "quantity": "<PERSON><PERSON> lượng có sẵn", "sku": "Mã SKU", "skuPlaceholder": "BASIC-001", "minQuantity": "<PERSON><PERSON> lượng tối thiểu mỗi lần mua", "maxQuantity": "<PERSON><PERSON> lượng tối đa mỗi lần mua", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "statusPending": "Chờ xử lý", "statusActive": "<PERSON><PERSON><PERSON> đ<PERSON>", "statusInactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "removeVersion": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>n"}, "priceDescriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả giá", "priceTypePlaceholder": "<PERSON><PERSON>n lo<PERSON>i giá", "priceTypes": {"yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "other": "K<PERSON><PERSON><PERSON>"}}}, "warehouse": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý kho hàng", "name": "<PERSON><PERSON><PERSON> kho", "code": "<PERSON><PERSON> kho", "desc": "<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON> kho", "types": {"PHYSICAL": "<PERSON><PERSON> v<PERSON> lý", "VIRTUAL": "<PERSON><PERSON> <PERSON><PERSON>"}, "status": {"title": "<PERSON><PERSON><PERSON><PERSON> thái"}, "address": "Địa chỉ", "contact": "<PERSON>h<PERSON>ng tin liên hệ", "add": "<PERSON><PERSON><PERSON><PERSON> kho", "edit": "Chỉnh sửa kho", "addForm": "<PERSON><PERSON><PERSON><PERSON> kho mới", "editForm": "Chỉnh sửa thông tin kho", "createSuccess": "<PERSON><PERSON><PERSON> kho thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nhật kho thành công", "deleteSuccess": "<PERSON><PERSON><PERSON> kho thành công", "createError": "Lỗi khi tạo kho", "updateError": "Lỗi khi cập nhật kho", "deleteError": "Lỗi khi xóa kho", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa kho này không?", "form": {"namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên kho", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả kho", "typePlaceholder": "<PERSON><PERSON><PERSON> lo<PERSON> kho", "selectType": "<PERSON><PERSON><PERSON> lo<PERSON> kho"}, "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kho", "associatedSystem": "<PERSON><PERSON> thống liên kết", "purpose": "<PERSON><PERSON><PERSON> đ<PERSON>ch sử dụng", "viewDetail": "<PERSON>em chi tiết", "detailTitle": "<PERSON> tiết kho", "virtual": {"detailTitle": "<PERSON> ti<PERSON>t kho <PERSON>o"}}, "folder": {"title": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON> th<PERSON> mục", "path": "Đường dẫn", "contents": "<PERSON><PERSON><PERSON> dung thư mục", "rootFolders": "<PERSON><PERSON><PERSON> m<PERSON>c", "noFolders": "<PERSON><PERSON><PERSON><PERSON> có thư mục nào", "create": "<PERSON><PERSON><PERSON> thư mục", "edit": "<PERSON><PERSON><PERSON> th<PERSON> mục", "delete": "<PERSON><PERSON><PERSON> th<PERSON> mục", "deleteSuccess": "<PERSON><PERSON><PERSON> thư mục thành công", "deleteConfirmTitle": "<PERSON><PERSON><PERSON> n<PERSON>n x<PERSON><PERSON> thư mục", "deleteConfirmMessage": "Bạn có chắc chắn muốn xóa {{count}} thư mục đã chọn?"}, "file": {"title": "<PERSON><PERSON>p tin", "name": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "upload": "<PERSON><PERSON><PERSON> lên t<PERSON>", "noFiles": "<PERSON><PERSON><PERSON><PERSON> có tệp nào", "deleteSuccess": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> thành công", "deleteConfirmTitle": "<PERSON><PERSON><PERSON> n<PERSON>n x<PERSON><PERSON> tệp", "deleteConfirmMessage": "Bạn có chắc chắn muốn xóa {{count}} tệp đã chọn?"}, "physicalWarehouse": {"title": "<PERSON><PERSON> v<PERSON> lý", "description": "<PERSON><PERSON><PERSON><PERSON> lý kho vật lý", "manage": "<PERSON><PERSON><PERSON><PERSON> lý kho vật lý", "totalWarehouses": "tổng số kho vật lý", "name": "<PERSON><PERSON><PERSON> kho vật lý", "warehouse": "<PERSON><PERSON>", "address": "Địa chỉ", "capacity": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON> kho vật lý", "create": "<PERSON><PERSON><PERSON> kho vật lý", "edit": "<PERSON><PERSON><PERSON> kho vật lý", "delete": "<PERSON><PERSON><PERSON> kho vật lý", "view": "<PERSON>em chi tiết", "search": "<PERSON><PERSON><PERSON> kiếm kho vật lý...", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu kho vật lý", "createSuccess": "<PERSON><PERSON><PERSON> kho vật lý thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nhật kho vật lý thành công", "deleteSuccess": "<PERSON><PERSON><PERSON> kho vật lý thành công", "deleteMultipleSuccess": "<PERSON><PERSON><PERSON> nhi<PERSON>u kho vật lý thành công", "createError": "<PERSON><PERSON><PERSON> kho vật lý thất bại", "updateError": "<PERSON><PERSON><PERSON> nhật kho vật lý thất bại", "deleteError": "<PERSON><PERSON><PERSON> kho vật lý thất bại", "deleteMultipleError": "<PERSON><PERSON><PERSON> nhi<PERSON>u kho vật lý thất bại", "selectToDelete": "<PERSON><PERSON> lòng chọn ít nhất một kho vật lý để xóa", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa kho vật lý này?", "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa {{count}} kho vật lý đã chọn?", "form": {"createTitle": "<PERSON><PERSON><PERSON> kho vật lý mới", "editTitle": "Chỉnh sửa kho vật lý", "create": "<PERSON><PERSON><PERSON> kho vật lý", "update": "<PERSON><PERSON><PERSON>", "selectWarehouse": "<PERSON><PERSON><PERSON> kho", "warehousePlaceholder": "<PERSON><PERSON><PERSON> kho để tạo kho vật lý", "warehouseRequired": "<PERSON><PERSON> l<PERSON> b<PERSON><PERSON> bu<PERSON>c", "addressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ kho vật lý", "addressRequired": "Địa chỉ là bắt buộc", "addressMaxLength": "Địa chỉ không đư<PERSON><PERSON> vư<PERSON><PERSON> quá 255 ký tự", "capacityPlaceholder": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON> ch<PERSON> kho", "capacityMin": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> ph<PERSON>i lớn hơn hoặc bằng 0"}}, "virtualWarehouse": {"title": "<PERSON><PERSON> <PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý kho <PERSON>o và hệ thống lưu trữ số", "manage": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "totalWarehouses": "tổng số kho <PERSON>o", "name": "<PERSON><PERSON><PERSON>", "status": {"title": "<PERSON><PERSON><PERSON><PERSON> thái", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động"}, "associatedSystem": "<PERSON><PERSON> thống liên kết", "purpose": "<PERSON><PERSON><PERSON> đ<PERSON>ch sử dụng", "actions": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "view": "<PERSON>em chi tiết", "search": "<PERSON><PERSON><PERSON> k<PERSON>m kho <PERSON>...", "noData": "<PERSON><PERSON><PERSON>ng có dữ liệu kho <PERSON>o", "createSuccess": "<PERSON><PERSON><PERSON> kho <PERSON>o thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t kho <PERSON>o thành công", "deleteSuccess": "<PERSON><PERSON><PERSON> kho <PERSON>o thành công", "deleteMultipleSuccess": "<PERSON><PERSON><PERSON> nhi<PERSON>u kho <PERSON>o thành công", "createError": "<PERSON><PERSON><PERSON> <PERSON>ho <PERSON>o thất bại", "updateError": "<PERSON><PERSON><PERSON> nh<PERSON>t kho <PERSON>o thất bại", "deleteError": "<PERSON><PERSON><PERSON>ho <PERSON>o thất b<PERSON>i", "deleteMultipleError": "<PERSON><PERSON><PERSON> n<PERSON>u kho <PERSON>o thất bại", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa kho <PERSON>o nà<PERSON>?", "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa {{count}} kho ảo đã chọn?", "form": {"createTitle": "<PERSON><PERSON><PERSON> kho <PERSON>o mới", "editTitle": "Chỉnh sửa kho <PERSON>o", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "warehousePlaceholder": "<PERSON><PERSON><PERSON> kho để tạo kho ảo", "warehouseRequired": "<PERSON><PERSON> l<PERSON> b<PERSON><PERSON> bu<PERSON>c", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả kho <PERSON>o", "descriptionMaxLength": "<PERSON><PERSON> tả không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 500 ký tự", "associatedSystemPlaceholder": "<PERSON><PERSON><PERSON><PERSON> hệ thống liên kết", "associatedSystemMaxLength": "<PERSON><PERSON> thống liên kết không đ<PERSON><PERSON><PERSON> vư<PERSON><PERSON> quá 200 ký tự", "purposePlaceholder": "<PERSON><PERSON><PERSON><PERSON> mục đ<PERSON>ch sử dụng", "purposeMaxLength": "<PERSON><PERSON><PERSON> đ<PERSON>ch sử dụng không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 300 ký tự", "statusPlaceholder": "<PERSON><PERSON><PERSON> trạng thái", "statusRequired": "<PERSON>r<PERSON><PERSON> thái là bắ<PERSON> bu<PERSON>c"}}}}