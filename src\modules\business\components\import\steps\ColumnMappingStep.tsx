import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Table,
  Icon,
  Select,
  Checkbox,
  IconCard,
} from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import {
  ExcelData,
  ColumnMapping,
  CustomerField,
  CustomerFieldKey,
} from '../../../types/customer-import.types';

interface ColumnMappingStepProps {
  excelData: ExcelData;
  existingMappings?: ColumnMapping[];
  onMappingComplete: (mappings: ColumnMapping[]) => void;
  onGoBack: () => void;
}

/**
 * Component cho bước mapping columns Excel với trường khách hàng
 */
const ColumnMappingStep: React.FC<ColumnMappingStepProps> = ({
  excelData,
  existingMappings,
  onMappingComplete,
  onGoBack,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Danh sách trường khách hàng có thể map
  const customerFields: CustomerField[] = useMemo(
    () => [
      {
        key: CustomerFieldKey.NAME,
        label: t('customer.form.name'),
        type: 'text',
        required: true,
        isCustomField: false,
      },
      {
        key: CustomerFieldKey.EMAIL,
        label: t('customer.form.email'),
        type: 'email',
        required: false,
        isCustomField: false,
      },
      {
        key: CustomerFieldKey.PHONE,
        label: t('customer.form.phone'),
        type: 'phone',
        required: false,
        isCustomField: false,
      },
      {
        key: CustomerFieldKey.ADDRESS,
        label: t('customer.form.address'),
        type: 'text',
        required: false,
        isCustomField: false,
      },
      {
        key: CustomerFieldKey.TAGS,
        label: t('customer.form.tags'),
        type: 'text',
        required: false,
        isCustomField: false,
      },
      // TODO: Thêm custom fields từ API
    ],
    [t]
  );

  // State cho mappings - sử dụng từ props nếu có, nếu không thì auto-mapping
  const [mappings, setMappings] = useState<ColumnMapping[]>(() => {
    // Nếu đã có mappings từ import state thì sử dụng
    if (existingMappings && existingMappings.length > 0) {
      return existingMappings;
    }

    // Auto-mapping dựa trên tên cột
    return excelData.headers.map(header => {
      const lowerHeader = header.toLowerCase().trim();
      let customerField = '';

      // Auto-detect common field names
      if (
        lowerHeader.includes('name') ||
        lowerHeader.includes('tên') ||
        lowerHeader.includes('họ')
      ) {
        customerField = CustomerFieldKey.NAME;
      } else if (lowerHeader.includes('email') || lowerHeader.includes('mail')) {
        customerField = CustomerFieldKey.EMAIL;
      } else if (
        lowerHeader.includes('phone') ||
        lowerHeader.includes('số điện thoại') ||
        lowerHeader.includes('sdt')
      ) {
        customerField = CustomerFieldKey.PHONE;
      } else if (lowerHeader.includes('address') || lowerHeader.includes('địa chỉ')) {
        customerField = CustomerFieldKey.ADDRESS;
      } else if (lowerHeader.includes('tag') || lowerHeader.includes('nhãn')) {
        customerField = CustomerFieldKey.TAGS;
      }

      return {
        excelColumn: header,
        customerField,
        isRequired: customerField === CustomerFieldKey.NAME,
      };
    });
  });

  // State cho checkbox xem trước dữ liệu
  const [showPreview, setShowPreview] = useState(false);

  // Cập nhật mapping cho một cột
  const updateMapping = (excelColumn: string, customerField: string) => {
    setMappings(prev =>
      prev.map(mapping =>
        mapping.excelColumn === excelColumn
          ? {
              ...mapping,
              customerField,
              isRequired: customerFields.find(f => f.key === customerField)?.required || false,
            }
          : mapping
      )
    );
  };

  // Kiểm tra validation tổng thể
  const globalValidationErrors = useMemo(() => {
    const errors: string[] = [];

    // Kiểm tra trường bắt buộc
    const requiredFields = customerFields.filter(f => f.required);
    requiredFields.forEach(field => {
      const isMapped = mappings.some(m => m.customerField === field.key);
      if (!isMapped) {
        errors.push(
          t('customer.import.mapping.errors.requiredFieldMissing', { field: field.label })
        );
      }
    });

    return errors;
  }, [mappings, customerFields, t]);

  // Xử lý tiếp tục
  const handleContinue = () => {
    if (globalValidationErrors.length === 0) {
      onMappingComplete(mappings);
    }
  };

  // Columns cho table preview
  const previewColumns: TableColumn<Record<string, unknown>>[] = [
    {
      key: 'index',
      title: '#',
      render: (_, __, index) => index + 1,
      width: 50,
    },
    ...excelData.headers.map(header => ({
      key: header,
      title: header,
      dataIndex: header,
      render: (value: unknown) => {
        const strValue = value ? String(value) : '';
        return strValue.length > 50 ? `${strValue.substring(0, 50)}...` : strValue;
      },
    })),
  ];

  // Data cho table preview (tất cả dữ liệu để có phân trang)
  const previewData = useMemo(() => {
    return excelData.rows.map((row, index) => {
      const rowData: Record<string, unknown> = { id: index };
      excelData.headers.forEach((header, headerIndex) => {
        rowData[header] = row[headerIndex];
      });
      return rowData;
    });
  }, [excelData]);

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="w-full text-center">
        <Typography variant="h5" className="mb-2">
          {t('customer.import.mapping.title')}
        </Typography>
        <Typography variant="body2" className="text-muted">
          {t('customer.import.mapping.description')}
        </Typography>
      </div>

      {/* File info */}
      <div className="p-4 bg-muted/50 rounded-lg">
        <div className="flex items-center space-x-4">
          <Icon name="file-text" size="sm" />
          <div>
            <Typography variant="body2" className="font-medium">
              {excelData.fileName}
            </Typography>
            <Typography variant="body2" className="text-muted text-xs">
              {excelData.headers.length} cột, {excelData.rows.length} dòng
            </Typography>
          </div>
        </div>
      </div>

      {/* Column Mapping */}
      <div className="p-6 bg-card rounded-lg">
        <Typography variant="h6" className="mb-4">
          {t('customer.import.mapping.columnMapping')}
        </Typography>

        <div className="space-y-4">
          {excelData.headers.map(header => {
            const mapping = mappings.find(m => m.excelColumn === header);
            const selectedField = customerFields.find(f => f.key === mapping?.customerField);

            // Kiểm tra duplicate mapping
            const isDuplicate =
              mapping?.customerField &&
              mappings.filter(m => m.customerField === mapping.customerField).length > 1;

            return (
              <div key={header} className="flex items-center space-x-4 p-3 bg-muted/30 rounded-lg">
                <div className="flex-1">
                  <Typography variant="body2" className="font-medium">
                    {header}
                  </Typography>
                  <Typography variant="body2" className="text-muted text-xs">
                    Excel Column
                  </Typography>
                </div>

                <Icon name="arrow-right" size="sm" className="text-muted" />

                <div className="flex-1">
                  <Select
                    value={mapping?.customerField || ''}
                    onChange={value => updateMapping(header, value as string)}
                    placeholder={t('customer.import.mapping.skipColumn')}
                    error={
                      isDuplicate ? t('customer.import.mapping.errors.duplicateMapping') : undefined
                    }
                    options={[
                      { value: '', label: t('customer.import.mapping.skipColumn') },
                      ...customerFields.map(field => ({
                        value: field.key,
                        label: `${field.label}${field.required ? ' *' : ''}`,
                      })),
                    ]}
                  />
                  {selectedField?.required && !isDuplicate && (
                    <Typography variant="body2" className="text-xs text-orange-600 mt-1">
                      {t('customer.import.mapping.requiredField')}
                    </Typography>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Preview Data */}
      <div className="p-6 bg-card rounded-lg">
        <Checkbox checked={showPreview} onChange={setShowPreview} label="Xem trước dữ liệu" />

        {showPreview && (
          <Table
            columns={previewColumns}
            data={previewData}
            rowKey="id"
            pagination={true}
            size="sm"
          />
        )}
      </div>

      {/* Global Validation Errors */}
      {globalValidationErrors.length > 0 && (
        <div className="p-4 border border-red-200 bg-red-50 rounded-lg">
          <div className="flex items-start space-x-2">
            <Icon name="alert-circle" size="sm" className="text-red-500 mt-0.5" />
            <div>
              <Typography variant="body2" className="font-medium text-red-700 mb-2">
                {t('customer.import.mapping.validationErrors')}
              </Typography>
              <ul className="space-y-1">
                {globalValidationErrors.map((error: string, index: number) => (
                  <li key={index} className="text-sm text-red-600">
                    • {error}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex">
        <IconCard icon="back" onClick={onGoBack} title={t('common.back')} />
        <IconCard
          icon="check"
          onClick={handleContinue}
          disabled={globalValidationErrors.length > 0}
          title={t('common.continue')}
        />
      </div>
    </div>
  );
};

export default ColumnMappingStep;
